# AI Bull

https://theaibull.com/

```
poetry install
poetry run uvicorn app.main:app --reload
```

### install ta-lib (for those who complain poetry install is not working for me)

```shell
sudo apt-get update
sudo apt-get install python3-dev


wget https://github.com/ta-lib/ta-lib/releases/download/v0.6.4/ta-lib_0.6.4_amd64.deb

sudo dpkg -i ta-lib_0.6.4_amd64.deb
```



https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/FO_NIFTY_APR_24_2025_23150_PE?interval=1m&start=2025-03-15&end=2025-03-19
https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/FO_NIFTY_Apr_24_2025_23150_PE?interval=1m&start=2025-03-24&end=2025-04-01



## New Routes for Data using Dhan..

http://localhost:8001/data/expirations/sbin -> Expirations
http://localhost:8001/data/ohlc/latest/sbin -> Last Candle
http://localhost:8001/data/options/sbin/2025-08-28 -> Options for that expiry (latest)

http://localhost:8001/data/ohlc/intraday/sbin/2025-08-01/2025-08-11/15 -> ohlc intraday
http://localhost:8001/data/ohlc/historical/sbin/2025-08-01/2025-08-11

# Market Quotes
http://localhost:8001/data/quote/symbol/sbin
http://localhost:8001/data/ltp/symbol/sbin

# Futures
http://localhost:8001/data/futures/sbin


