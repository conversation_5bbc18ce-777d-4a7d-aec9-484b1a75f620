from typing import Optional

from fastapi import APIRouter, Query

from app.util.dhan_core.client import (
    get_expirations,
    get_futures,
    get_ohlc_data,
    get_option_chain,
    get_quote_data_by_symbol,
    get_ticker_data_by_symbol,
    ohlc_historical,
    ohlc_intraday,
)

router = APIRouter()


@router.get("/expirations/{symbol}")
def api_get_expirations(symbol: str):
    """
    Get expiry list for a given underlying symbol (as a path parameter).
    Returns a dict with symbol and expirations.
    """
    expirations = get_expirations(symbol)
    return {"symbol": symbol, "expirations": expirations}


## we will deprecate this to get for ALL expirations which is very heavy
# @router.get("/options/{symbol}")
# def api_get_option_chain(symbol: str, expiry: Optional[str] = None):
#     """
#     Get option chain for a symbol, and (optional) expiry.
#     """
#     return get_option_chain_with_all_expirations(symbol, expiry)


@router.get("/options/{symbol}/{expiry}")
def api_get_option_chain_with_expiry(symbol: str, expiry: str):
    """
    Get option chain for a symbol and a specific expiry.
    """
    return get_option_chain(symbol, expiry)


@router.get("/ohlc/latest/{symbol}")
def api_get_ohlc(symbol: str):
    """
    Get OHLC data for a given symbol (as a path parameter).
    """
    return get_ohlc_data(symbol)


@router.get("/ohlc/intraday/{symbol}/{from_date}/{to_date}/{interval}")
def api_get_ohlc_intraday(symbol: str, from_date: str, to_date: str, interval: int):
    """
    Get intraday OHLC (minute candles) for a symbol between from_date and to_date.
    interval: 1, 5, 15, 25, or 60 (minutes) as a path parameter (at the end).
    from_date and to_date as path parameters (YYYY-MM-DD).
    """
    return ohlc_intraday(symbol, from_date, to_date, interval)


@router.get("/ohlc/historical/{symbol}/{from_date}/{to_date}")
def api_get_historical_ohlc(symbol: str, from_date: str, to_date: str, expiry_code: int = 0):
    """
    Get historical daily OHLC for a symbol between from_date and to_date.
    expiry_code: 0 for stocks, 1/2/3 for derivatives if needed.
    from_date and to_date as path parameters (YYYY-MM-DD).
    """
    return ohlc_historical(symbol, from_date, to_date, expiry_code)


@router.get("/futures/{symbol}")
def api_get_futures(symbol: str):
    """
    Get all futures contracts for a given symbol (case-insensitive).
    Returns a list of dicts with expiry, security_id, display_name, and ticker_data.
    """
    results = get_futures(symbol)
    if not results:
        return []
    return results


## Maybe these will work only during the market hours.
@router.get("/quote/symbol/{symbol}")
def api_get_futures_quote(symbol: str):
    """
    Get full quote data for a given futures symbol.
    """
    return get_quote_data_by_symbol(symbol)


@router.get("/ltp/symbol/{symbol}")
def api_get_futures_ltp(symbol: str):
    """
    Get LTP (ticker) data for a given futures symbol.
    """
    return get_ticker_data_by_symbol(symbol)
