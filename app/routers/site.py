import asyncio
from typing import Any, Dict, Optional

import httpx
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>TTPEx<PERSON>, Request
from fastapi.responses import PlainTextResponse, RedirectResponse

from app.routers.blogs import get_blogs_data
from app.routers.indices import fetch_all_indices_data
from app.routers.screener import (
    SEO_STOCK_FILTER_MAPPINGS,
    SEO_STOCK_METADATA,
    ScreenerFilter,
    get_all_screener_data,
    get_filter_options,
    search_screener,
)
from app.util.fear_factor import tickertape_mmi
from app.util.menu import menu
from app.util.settings import app_settings
from app.util.stocks import (
    get_all_futures,
    get_all_stocks,
    get_all_stocks_live_analysis,
    get_all_stocks_technical_analysis,
    get_bse,
    get_most_active,
    get_stock_details,
    refresh,
)
from app.util.templates import templates

from .auth import get_user_data

router = APIRouter()


@router.get("/")
async def get_home(request: Request, access_token: str = <PERSON><PERSON>(None)):
    user_data = get_user_data(access_token)
    blogs = await get_blogs_data(limit=3)  # Get only 3 blogs for the homepage
    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            **menu,
            **user_data,
            "active_stocks": get_most_active(),
            "blogs": blogs,
        },
    )


@router.get("/app")
async def get_app(request: Request, access_token: str = Cookie(None)):
    user_data = get_user_data(access_token)

    async def get_live_analysis():
        return get_all_stocks_live_analysis()

    async def get_score():
        return tickertape_mmi()

    # Run concurrently
    live_analysis, score = await asyncio.gather(
        get_live_analysis(),
        get_score(),
    )

    derivatives = live_analysis.get("derivatives", {}).get("most_active", {})
    capital = live_analysis.get("capital", {})

    return templates.TemplateResponse(
        "app.html",
        {
            "request": request,
            **menu,
            **user_data,
            "active_stocks": get_most_active(),
            "derivatives": derivatives,
            "capital": capital,
            "fear_and_greed": {"score": score},
        },
    )


@router.get("/app/indices")
async def get_indices():
    return await fetch_all_indices_data()


@router.get("/options/chain/{symbol}")
@router.get("/options/chain")
async def get_option_chain(request: Request, symbol: Optional[str] = None, access_token: str = Cookie(None)):
    if symbol and not symbol.isalnum():
        raise HTTPException(status_code=400, detail="Invalid stock symbol")
    else:
        symbol = symbol.lower() if symbol else ""

    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "options/chain.html",
        {
            "request": request,
            **menu,
            "active_stocks": active_stocks,
            "symbol": symbol or "",
            **get_user_data(access_token),
        },
    )


@router.get("/options/spreads")
async def get_spreads(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()

    return templates.TemplateResponse(
        "options/spreads/main.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/covered-call")
async def get_covered_call(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()

    return templates.TemplateResponse(
        "strategies/covered-call/covered-call.html",
        {
            "request": request,
            **menu,
            "active_stocks": active_stocks,
            **get_user_data(access_token),
        },
    )


@router.get("/strategies/cash-secured-put")
async def get_cash_secured_put(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/cash-secured-put/cash-secured-put.html",
        {
            "request": request,
            **menu,
            "active_stocks": active_stocks,
            **get_user_data(access_token),
        },
    )


@router.get("/strategies/futures-arbitrage")
async def get_futures_arbitrage(request: Request, access_token: str = Cookie(None)):
    futures = get_all_futures()
    stocks = get_all_stocks()
    return templates.TemplateResponse(
        "strategies/futures-arbitrage.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "futures": futures,
            "stocks": stocks,
        },
    )


@router.get("/strategies/exchanges-arbitrage")
async def get_futures_arbitrage(request: Request, access_token: str = Cookie(None)):
    bse = get_bse()
    stocks = get_all_stocks()
    return templates.TemplateResponse(
        "strategies/exchange-arbitrage.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "bse": bse,
            "stocks": stocks,
        },
    )


@router.get("/strategies/put-call-parity")
async def get_put_call_parity(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/put-call-parity/put-call-parity.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/box-spread-arbitrage")
async def get_box_spread_arbitrage(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/box-spread-arbitrage/box-spread-arbitrage.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/strike-scanner")
async def get_delta_scanner(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/strike-scanner.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/spread-analyzer")
async def get_delta_scanner(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/spread-analyzer.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/straddle-scanner")
async def get_straddle_scanner(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/straddle-scanner.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/straddle-graph")
async def get_straddle_scanner(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/straddle-graph.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


@router.get("/strategies/straddle-graph-intraday")
async def get_straddle_graph_intraday(request: Request, access_token: str = Cookie(None)):
    active_stocks = get_most_active()
    return templates.TemplateResponse(
        "strategies/straddle-graph-intraday.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "active_stocks": active_stocks,
        },
    )


Apps = None  # Cache for apps


@router.get("/apps")
async def get_apps(request: Request, access_token: str = Cookie(None)):
    global Apps

    if Apps is None:
        # Fetch data from the external API
        async with httpx.AsyncClient() as client:
            response = await client.get("https://apps.theaibull.com/apps/all", timeout=10)
            response.raise_for_status()
            Apps = response.json()

    # Filter the apps to only include those with category "Stocks"
    filtered_apps = [app for app in Apps if app.get("category") == "Stocks"]

    # Render the template with the necessary context data
    return templates.TemplateResponse(
        "apps/apps.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "apps": filtered_apps,  # Pass the filtered apps
        },
    )


@router.get("/stocks/top")
async def get_top_stocks(request: Request, access_token: str = Cookie(None)):
    # Get the cached stocks analysis data.
    technical_analysis = get_all_stocks_technical_analysis()
    active_stocks = get_most_active()
    # Build the template context. We merge user data with the stocks data.
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "active_stocks": active_stocks,
        "analyzed_stocks": technical_analysis,
    }
    return templates.TemplateResponse("stocks/top.html", context)


# Configuration for stock categories
STOCK_CATEGORIES = {
    "": {  # Default route
        "category": "active",
        "title": "Live Stock Market Data - Real-Time Analysis",
        "description": "Access real-time stock market data with live updates on prices, trading volume, and market trends for informed investment decisions.",
    },
    "active": {
        "category": "active",
        "title": "Most Active Stocks - Live Trading Data",
        "description": "Explore the most active stocks with real-time data on trading volume and market activity to identify high-momentum opportunities.",
    },
    "advances-declines": {
        "category": "advances_declines",
        "title": "Market Advances & Declines - Live Data",
        "description": "Monitor real-time market breadth with advances and declines data to gauge overall stock market strength and sentiment.",
    },
    "gainers": {
        "category": "gainers",
        "title": "Top Gainers - Highest Price Increases",
        "description": "Discover today's top-performing stocks with the highest price increases, updated live for strategic investment insights.",
    },
    "losers": {
        "category": "loosers",  # Note: The API uses "loosers" instead of "losers"
        "title": "Top Losers - Largest Price Decreases",
        "description": "Track stocks with the largest price drops today, with real-time updates to identify potential buying opportunities.",
    },
    "new-52week": {
        "category": "new_52week",
        "title": "New 52-Week Highs - Top Stocks Today",
        "description": "Find stocks reaching new 52-week highs with live data, highlighting strong performers and market trends.",
    },
    "price-band-hitters": {
        "category": "price_band_hitters",
        "title": "Price Band Hitters - Circuit Limits",
        "description": "Identify stocks hitting upper or lower circuit limits in real-time, signaling significant price movements and volatility.",
    },
    "price-spurts": {
        "category": "price_spurts",
        "title": "Price Spurts - Major Stock Movements",
        "description": "Monitor stocks experiencing significant price surges with live updates, ideal for spotting breakout opportunities.",
    },
    "volume-gainers": {
        "category": "volume_gainers",
        "title": "Volume Gainers - Top Trading Volume",
        "description": "Discover stocks with the highest trading volume increases today, reflecting strong market interest and potential trends.",
    },
}

# Configuration for derivatives categories
DERIVATIVES_CATEGORIES = {
    "contracts": {
        "legend": "contracts",
        "title": "Active Contracts - Live Trading Data",
        "description": "Explore the most active derivatives contracts with real-time trading data, offering insights into market activity and trends.",
    },
    "futures": {
        "legend": "futures",
        "title": "Futures Contracts - Live Market Data",
        "description": "Track the most active futures contracts with live data, providing key insights for futures trading strategies.",
    },
    "options": {
        "legend": "options",
        "title": "Options Contracts - Live Trading Data",
        "description": "Analyze the most active options contracts with real-time updates, ideal for options traders seeking market opportunities.",
    },
    "index-calls": {
        "legend": "index_calls",
        "title": "Index Call Options - Live Market Data",
        "description": "Discover the most active index call options with live trading data, highlighting bullish market sentiment.",
    },
    "index-puts": {
        "legend": "index_puts",
        "title": "Index Put Options - Live Market Data",
        "description": "Monitor the most active index put options with real-time data, reflecting bearish market trends and opportunities.",
    },
    "stock-calls": {
        "legend": "stock_calls",
        "title": "Stock Call Options - Live Trading Data",
        "description": "Track the most active stock call options with live updates, offering insights into bullish stock-specific trading activity.",
    },
    "stock-puts": {
        "legend": "stock_puts",
        "title": "Stock Put Options - Live Trading Data",
        "description": "Explore the most active stock put options with real-time data, identifying bearish trends and trading opportunities.",
    },
}


# Helper function to handle live market data routes
async def handle_live_market_data(request: Request, access_token: str, category_type: str, category_key: str):
    """
    Generic handler for live market data routes.

    Args:
        request: The FastAPI request object
        access_token: The user's access token
        category_type: Either 'stocks' or 'derivatives'
        category_key: The specific category key from the configuration
    """
    # Get the cached stocks analysis data
    live_analysis = get_all_stocks_live_analysis()

    # Build the base context
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "live_analysis": live_analysis,
    }

    # Add category-specific context based on type
    if category_type == "stocks":
        config = STOCK_CATEGORIES.get(category_key, STOCK_CATEGORIES[""])
        context.update({"selected_category": config["category"], "selected_tab": "capital", "page_title": config["title"], "seo_description": config["description"]})
    elif category_type == "derivatives":
        config = DERIVATIVES_CATEGORIES.get(category_key, DERIVATIVES_CATEGORIES["contracts"])
        context.update(
            {
                "selected_tab": "derivatives",
                "selected_derivatives_category": "most_active",
                "selected_derivatives_legend": config["legend"],
                "page_title": config["title"],
                "seo_description": config["description"],
            }
        )

    return templates.TemplateResponse("stocks/live/main.html", context)


# Main live stocks route
@router.get("/stocks/live")
async def get_live_stocks(request: Request, access_token: str = Cookie(None)):
    return await handle_live_market_data(request, access_token, "stocks", "")


# Dynamic route for stock categories
@router.get("/stocks/live/{category}")
async def get_live_stocks_category(request: Request, category: str, access_token: str = Cookie(None)):
    if category in STOCK_CATEGORIES:
        return await handle_live_market_data(request, access_token, "stocks", category)
    elif category == "derivatives":
        # Handle the case where someone visits /stocks/live/derivatives without a subcategory
        return await handle_live_market_data(request, access_token, "derivatives", "contracts")
    else:
        # If category doesn't exist, redirect to main live page
        return RedirectResponse(url="/stocks/live")


# Dynamic route for derivatives categories
@router.get("/stocks/live/derivatives/{category}")
async def get_live_derivatives_category(request: Request, category: str, access_token: str = Cookie(None)):
    if category in DERIVATIVES_CATEGORIES:
        return await handle_live_market_data(request, access_token, "derivatives", category)
    else:
        # If category doesn't exist, redirect to main derivatives page
        return RedirectResponse(url="/stocks/live/derivatives")


@router.get("/stocks/screener2")
async def get_screener(request: Request, access_token: Optional[str] = Cookie(None)):
    """
    Render the stock screener page. If a symbol is provided, fetch its details.
    """

    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("stocks/details.html", context)


@router.get("/stocks")
@router.get("/stocks/{path_param:path}")
async def get_stock_screener_or_details(
    request: Request,
    path_param: str = "",
    access_token: Optional[str] = Cookie(None),
):
    """
    Handle stock details for valid symbols (e.g., /stocks/AAPL)
    or stock screener for SEO-friendly filter URLs (e.g., /stocks/long-term-gems)
    and the main screener page (/stocks).
    Supports pagination (e.g., /stocks/long-term-gems/2).
    Also handles industry and sector routes (e.g., /stocks/technology-sector, /stocks/software-industry).
    """
    try:
        # Build base context
        context = {
            "request": request,
            **menu,
            **get_user_data(access_token),
        }
        path_param = path_param.strip("/").lower()

        # Initialize pagination parameters
        page = 1
        limit = 100
        actual_path_param = path_param
        is_default_screener = False

        # Handle default screener cases: "" (page 1) or a number (e.g., "5")
        if not path_param or path_param.isdigit():
            is_default_screener = True
            if path_param.isdigit():
                page = int(path_param)
            actual_path_param = "stocks"
        # Handle SEO filter with page (e.g., "long-term-gems/2")
        elif "/" in path_param:
            parts = path_param.rsplit("/", 1)
            try:
                page = int(parts[1])
                actual_path_param = parts[0]
            except ValueError:
                page = 1
                actual_path_param = path_param

        # Ensure page is at least 1
        page = max(1, page)

        # Default columns to display
        default_columns = ["symbol", "name", "sector", "industry", "marketCap", "regularMarketPrice", "debtToEquity", "beta", "forwardPE", "dividendYield"]

        # Initialize filters
        filters = ScreenerFilter(columns=default_columns, page=page, limit=limit)
        preset_filters = {}

        # Handle SEO-friendly filter URLs, industry, and sector routes
        is_industry_route = actual_path_param.endswith("-industry")
        is_sector_route = actual_path_param.endswith("-sector")

        if is_industry_route or is_sector_route:
            # Extract the industry or sector name (remove "-industry" or "-sector" suffix)
            filter_value = actual_path_param.replace("-industry", "").replace("-sector", "")
            preset_filters = {
                "industry" if is_industry_route else "sector": filter_value.replace("-", " ").title(),
            }
            # Update filters with industry/sector and pagination
            filters = ScreenerFilter(**{k: v for k, v in preset_filters.items() if v is not None}, columns=default_columns, page=page, limit=limit)
        elif actual_path_param in SEO_STOCK_FILTER_MAPPINGS:
            filter_params = SEO_STOCK_FILTER_MAPPINGS[actual_path_param]
            preset_filters = {
                "sector": filter_params.get("sector", ""),
                "min_market_cap": filter_params.get("min_market_cap") * 10000000 if filter_params.get("min_market_cap") is not None else None,
                "max_market_cap": filter_params.get("max_market_cap") * 10000000 if filter_params.get("max_market_cap") is not None else None,
                "min_close_price": filter_params.get("min_close_price", None),
                "max_close_price": filter_params.get("max_close_price", None),
                "min_debt_to_equity": filter_params.get("min_debt_to_equity", None),
                "max_debt_to_equity": filter_params.get("max_debt_to_equity", None),
                "min_beta": filter_params.get("min_beta", None),
                "max_beta": filter_params.get("max_beta", None),
                "min_forward_pe": filter_params.get("min_forward_pe", None),
                "max_forward_pe": filter_params.get("max_forward_pe", None),
                "min_dividend_yield": filter_params.get("min_dividend_yield", None),
                "max_dividend_yield": filter_params.get("max_dividend_yield", None),
            }
            # Update filters with preset values and pagination
            filters = ScreenerFilter(**{k: v for k, v in preset_filters.items() if v is not None}, columns=default_columns, page=page, limit=limit)

        # Fetch initial stock data using search_screener
        search_result = search_screener(filters)
        initial_stocks = search_result["stocks"]
        total = search_result["count"]

        # Special case for the main screener page or SEO filter pages
        if actual_path_param == "stocks":
            canonical_url = f"https://theaibull.com/stocks" if page == 1 else f"https://theaibull.com/stocks/{page}"
        else:
            canonical_url = f"https://theaibull.com/stocks/{actual_path_param}" if page == 1 else f"https://theaibull.com/stocks/{actual_path_param}/{page}"
        canonical_url = canonical_url.rstrip("/")

        context.update(
            {
                "filters": get_filter_options(),
                "preset_filters": preset_filters,
                "active_stocks": get_most_active(),
                "initial_stocks": initial_stocks,
                "total": total,
                "selected_columns": default_columns,
                "seo_title": SEO_STOCK_METADATA.get(actual_path_param, {}).get(
                    "title",
                    (
                        f"{'Industry' if is_industry_route else 'Sector'} Stock Screener - {filter_value.replace('-', ' ').title()}"
                        if (is_industry_route or is_sector_route)
                        else "Stock Screener - Top Investment Opportunities"
                    ),
                ),
                "seo_description": SEO_STOCK_METADATA.get(actual_path_param, {}).get(
                    "description", "Explore top stocks with advanced screening. Filter by sector, market cap, and more to find the best investments."
                ),
                "canonical_url": canonical_url,
                "total_pages": (total + limit - 1) // limit if total else 1,
                "current_page": page,
                "limit": limit,
                "path_param": actual_path_param,
            }
        )

        # Try to treat path_param as a stock symbol
        if path_param and actual_path_param not in SEO_STOCK_FILTER_MAPPINGS and not (is_industry_route or is_sector_route):
            stock_details = None
            symbol = path_param.upper()
            try:
                stock_details = get_stock_details(symbol)
            except Exception:
                stock_details = None

            if stock_details:
                # Existing logic for stock details page
                filter_keys = list(SEO_STOCK_FILTER_MAPPINGS.keys())
                sector = stock_details.get("industryInfo", {}).get("sector", "").lower().replace(" ", "-")
                sector_filter_key = f"{sector}-stocks" if sector else None
                current_index = filter_keys.index(sector_filter_key) if sector_filter_key in filter_keys else 0

                selected_filter_keys = (filter_keys[max(0, current_index - 4) : current_index] + filter_keys[current_index + 1 : current_index + 5])[:8]
                if len(selected_filter_keys) < 8:
                    available_keys = [k for k in filter_keys if k not in selected_filter_keys]
                    selected_filter_keys.extend(available_keys[: 8 - len(selected_filter_keys)])

                random_filters = [
                    {
                        "url": key,
                        "title": SEO_STOCK_METADATA.get(key, {}).get("title", "Stock Screener").replace(" - Top Picks", "").replace(" - Best Picks", ""),
                        "description": SEO_STOCK_METADATA.get(key, {}).get("description", "Explore top stocks with advanced screening."),
                    }
                    for key in selected_filter_keys
                ]

                interested_stocks = []
                primary_industry = stock_details.get("info", {}).get("industry", None)
                if primary_industry:
                    all_stocks = get_all_screener_data()
                    industry_stocks = [{**stock, "symbol": symbol} for symbol, stock in all_stocks.items() if stock.get("info", {}).get("industry") == primary_industry]
                    industry_stocks.sort(key=lambda x: (float(x.get("info", {}).get("marketCap", 0) or 0), float(x.get("info", {}).get("fiftyTwoWeekChangePercent", 0) or 0)), reverse=True)
                    selected_symbol = symbol.replace(".NS", "").upper()
                    indices = [i for i, stock in enumerate(industry_stocks) if stock["symbol"].replace(".NS", "").upper() == selected_symbol]
                    if indices:
                        min_index = min(indices)
                        max_index = max(indices)
                        start_index = max(0, min_index - 5)
                        end_index = min(len(industry_stocks), max_index + 6)
                        interested_stocks = [
                            {
                                "symbol": stock["symbol"],
                                "info": {
                                    "companyName": stock.get("info", {}).get("companyName", "Unknown Stock"),
                                    "longName": stock.get("info", {}).get("longName", stock["symbol"]),
                                    "sector": stock.get("info", {}).get("sector", "Unknown Sector"),
                                    "industry": stock.get("info", {}).get("industry", "Unknown Industry"),
                                },
                            }
                            for stock in industry_stocks[start_index:end_index]
                            if stock["symbol"].replace(".NS", "").upper() != selected_symbol
                        ][:10]
                        if len(interested_stocks) < 10:
                            additional_stocks = [
                                {
                                    "symbol": stock["symbol"],
                                    "info": {
                                        "companyName": stock.get("info", {}).get("companyName", "Unknown Stock"),
                                        "longName": stock.get("info", {}).get("longName", stock["symbol"]),
                                        "sector": stock.get("info", {}).get("sector", "Unknown Sector"),
                                        "industry": stock.get("info", {}).get("industry", "Unknown Industry"),
                                    },
                                }
                                for stock in industry_stocks
                                if stock["symbol"].replace(".NS", "").upper() != selected_symbol and stock not in [s for s in industry_stocks[start_index:end_index]]
                            ][: 10 - len(interested_stocks)]
                            interested_stocks.extend(additional_stocks)

                context.update(
                    {
                        "stock_data": stock_details,
                        "symbol": symbol,
                        "active_stocks": get_most_active(),
                        "random_filters": random_filters,
                        "interested_stocks": interested_stocks,
                    }
                )
                return templates.TemplateResponse("stocks/details.html", context)
            else:
                # Raise 404 for invalid stock symbol
                raise HTTPException(status_code=404, detail="Stock symbol not found")

        # Render screener page with initial stock data
        return templates.TemplateResponse("stocks/screener.html", context)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while processing the request: {str(e)}",
        )


@router.get("/algos/options")
async def algo_strategies(request: Request, symbol: Optional[str] = None, access_token: Optional[str] = Cookie(None)):

    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "symbol": symbol,
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("algos/options/main.html", context)


@router.get("/algos/backtesting")
async def algo_strategies(request: Request, symbol: Optional[str] = None, access_token: Optional[str] = Cookie(None)):

    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "symbol": symbol,
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("algos/backtesting/main.html", context)


# Do for backtesting-intraday
@router.get("/algos/backtesting-intraday")
async def algo_strategies_intraday(request: Request, symbol: Optional[str] = None, access_token: Optional[str] = Cookie(None)):

    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "symbol": symbol,
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("algos/backtesting-intraday/main.html", context)


@router.get("/algos/scalping")
async def algo_strategies(request: Request, symbol: Optional[str] = None, access_token: Optional[str] = Cookie(None)):

    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "symbol": symbol,
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("algos/scalping/main.html", context)


@router.get("/stocks/live/refresh")
async def refresh_stocks(request: Request, access_token: str = Cookie(None)):
    refresh()
    return "Thank you"


@router.get("/privacy-policy")
async def privacy_policy(request: Request, access_token: str = Cookie(None)):
    return templates.TemplateResponse("blocks/privacy-policy.html", {"request": request, **menu, **get_user_data(access_token)})


@router.get("/terms-conditions")
async def terms_conditions(request: Request, access_token: str = Cookie(None)):
    return templates.TemplateResponse("blocks/terms-conditions.html", {"request": request, **menu, **get_user_data(access_token)})


@router.get("/robots.txt")
async def robots_txt(request: Request):
    # Updated robots.txt content as we are going live now
    return PlainTextResponse("User-agent: Googlebot\nAllow: /\n\nUser-agent: *\nAllow: /\n\nSitemap: https://theaibull.com/sitemap.xml")


@router.get("/siteaudit-YjEyM2RhMj.txt")
async def serve_semrush_verification():
    return PlainTextResponse("YjEyM2RhMj")


@router.get("/technical-screeners/intraday")
async def intraday_screeners(request: Request, access_token: Optional[str] = Cookie(None)):
    active_stocks = get_most_active()
    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "active_stocks": active_stocks,
    }
    return templates.TemplateResponse("technical-screeners/intraday-screeners.html", context)
