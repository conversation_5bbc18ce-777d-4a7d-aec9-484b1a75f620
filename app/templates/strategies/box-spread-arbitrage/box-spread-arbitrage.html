<!DOCTYPE html>
<html lang="en">

<head>
    <title>Box Spread Arbitrage Scanner with AI Insights | AI Bull</title>
    <meta name="description"
        content="Discover arbitrage opportunities using box spreads by analyzing price discrepancies between long and short options. Maximize profits by exploiting mispriced spreads in the options market." />
    <link rel="canonical" href="https://theaibull.com/strategies/box-spread-arbitrage" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/box-spread-arbitrage" />
    <meta property="og:title" content="Box Spread Arbitrage Scanner - Find Arbitrage Opportunities Using Box Spreads" />
    <meta property="og:description"
        content="Discover arbitrage opportunities using box spreads by analyzing price discrepancies between long and short options. Maximize profits by exploiting mispriced spreads in the options market." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@yourhandle" />
    <meta name="twitter:title"
        content="Box Spread Arbitrage Scanner - Find Arbitrage Opportunities Using Box Spreads" />
    <meta name="twitter:description"
        content="Find arbitrage opportunities using box spreads by analyzing price discrepancies between long and short options to optimize your options trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {%include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">
                <div
                    class="flex flex-col md:flex-row justify-between bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold tracking-tight">
                            Box Spread Arbitrage Scanner
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Find arbitrage opportunities using box spreads
                        </p>
                        <!-- Button to trigger modal open -->
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                    <div class="rounded-lg bg-slate-100 p-0.5 w-[300px] mt-2 md:mt-0">
                        <nav class="-mb-px flex" aria-label="Tabs">
                            <button id="search-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center shadow-md font-medium text-sm text-blue-600 dark:text-blue-500 bg-white rounded-md"
                                aria-current="page">
                                Search Stock
                            </button>
                            <button id="all-stocks-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300">
                                Scan All Stocks
                            </button>
                        </nav>
                    </div>
                </div>
                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Left Column: Search and Most Active -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            {% include 'blocks/search-active-recent-stocks.html' %}
                        </div>

                        <!-- Right Column: Results -->
                        <div id="right-section-bsa" class="flex-1 bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div class="flex flex-col md:flex-row gap-3 md:items-center justify-between mb-2">
                                <div class="flex flex-col md:flex-row md:items-center justify-between md:gap-2 gap-3">
                                    <!-- Price Info -->
                                    <div id="stock-price-box"></div>
                                    <!-- Future Price Display -->
                                    <div id="future-price-box"></div>
                                </div>
                                <div class="mt-2">
                                    {% with id='box-spread-arbitrage-single-use-bid-ask' %}
                                    {% include 'blocks/use-ltp-checkbox.html' %}
                                    {% endwith %}
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row gap-3 md:items-center justify-between mb-2">
                                <!-- Volume Filter -->
                                <div id="filters-container" class="mb-4 w-[300px] hidden">
                                    <!-- Volume Filter -->
                                    <div>
                                        <label for="volume-filter-slider" class="block font-medium mb-2 text-sm">
                                            Volume Threshold (<span>1000</span>)
                                        </label>
                                        <div id="volume-filter-slider" class="mt-6"></div>
                                        <div class="flex justify-between text-sm mt-1">
                                            <span>0</span>
                                            <span>1000</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Expiry Tabs -->
                                <div id="expiry-tabs"
                                    class="border flex gap-0 mb-2 md:mb-0 overflow-x-auto p-1 rounded-md hidden text-nowrap">
                                </div>
                            </div>
                            <!-- Box Spread Table -->
                            <div id="option-chain-table" class="border-dashed border-t pt-3">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Stocks Tab Content -->
                {% include 'blocks/spreads-preview.html' %}
                <div id="all-stocks-content" class="tab-content hidden">
                    {% include 'strategies/box-spread-arbitrage/box-spread-arbitrage-scanner.html' %}
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a Box Spread Arbitrage?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A box spread arbitrage is a trading strategy that involves creating a combination of
                                    options positions (long and short)
                                    with the same expiration, designed to lock in risk-free profits by exploiting
                                    pricing discrepancies.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does the scanner identify arbitrage opportunities?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The scanner analyzes the prices of long and short call/put options with the same
                                    strike prices and expiration,
                                    identifying cases where the cost of entering the spread differs from the expected
                                    payoff, signaling potential arbitrage
                                    opportunities.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Can I use this scanner for any stock or index?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Yes, you can search for box spread arbitrage opportunities on individual stocks or
                                    indices. Simply enter a symbol or
                                    choose from popular stocks and indices.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What factors influence box spread arbitrage opportunities?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Key factors include pricing discrepancies, option premiums, and expiration dates.
                                    The scanner evaluates these factors to
                                    detect profitable arbitrage opportunities between long and short positions.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Are there risks associated with box spread arbitrage?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    While box spreads can be risk-free theoretically, execution risks such as slippage,
                                    transaction costs, or liquidity
                                    issues may reduce profits. It's crucial to consider these factors when entering a
                                    box spread arbitrage trade.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>

            </div>
        </main>
        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>

        {% include 'blocks/table-sorting.html' %}

        <script>
            // Global variables
            window._lastOptionData = {};

            document.addEventListener('DOMContentLoaded', () => {
                // DOM elements
                const expiryTabsContainer = document.getElementById('expiry-tabs');
                const tableContainer = document.getElementById('option-chain-table');
                const filtersContainer = document.getElementById('filters-container');
                const rightSection = document.getElementById('right-section-bsa');
                const stockPriceBoxContainer = document.getElementById('stock-price-box');
                const futurePriceBoxContainer = document.getElementById('future-price-box');
                window.futurePrices = null;
                let futurePrice = '';
                let ticker = '';
                let optionData = ''


                // Listen for custom event from search component
                document.addEventListener('stockSearchTriggered', (e) => {
                    const symbol = e.detail.symbol;
                    if (symbol) {
                        ticker = symbol;
                        handleSearch(symbol);
                    }
                });

                // Clear UI and fetch data for a symbol
                async function handleSearch(symbol) {
                    rightSection.classList.remove('hidden');
                    ticker = symbol;
                    showLoadingSpinner("right-section-bsa", `Loading data for ${symbol}`);
                    expiryTabsContainer.innerHTML = '';
                    tableContainer.innerHTML = '';
                    stockPriceBoxContainer.innerHTML = '';
                    futurePriceBoxContainer.innerHTML = '';
                    await fetchOptionChain(symbol);
                    await fetchFutures(symbol);
                    filtersContainer.classList.remove('hidden');
                    expiryTabsContainer.classList.remove('hidden');
                }

                async function fetchFutures(symbol) {
                    try {
                        const resFutures = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${symbol}`);
                        window.futurePrices = await resFutures.json();
                    } catch (error) {
                        console.error("Error fetching futures data", error);
                    } finally {
                        hideLoadingSpinner("right-section-bsa");
                    }
                }

                // Fetch option chain data
                async function fetchOptionChain(symbol) {
                    try {
                        const res = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`);
                        const data = await res.json();
                        let underlyingValue = null;
                        if (data.records && data.records.data) {
                            for (const record of data.records.data) {
                                if (record.CE && record.CE.underlyingValue) {
                                    underlyingValue = record.CE.underlyingValue;
                                    break;
                                }
                                if (record.PE && record.PE.underlyingValue) {
                                    underlyingValue = record.PE.underlyingValue;
                                    break;
                                }
                            }
                        }
                        window.currentStockPrice = underlyingValue;
                        stockPriceBoxContainer.innerHTML = `<span class="bg-sky-100 font-semibold px-3 py-1 rounded text-center">Stock Price: ${underlyingValue ? '₹' + underlyingValue.toFixed(2) : 'N/A'}</span>`;
                        renderExpiryTabs(data.records);
                        filtersContainer.classList.remove('hidden');
                    } catch (error) {
                        filtersContainer.classList.add('hidden');
                        console.error('Error fetching option chain:', error);
                        tableContainer.innerHTML = '<p class="text-red-500 text-sm">Failed to fetch options data</p>';
                    }
                }

                // Render expiry tabs
                function renderExpiryTabs(data) {
                    if (!data.expiryDates || !data.expiryDates.length) {
                        tableContainer.innerHTML = '<p>No expiry data found.</p>';
                        return;
                    }

                    expiryTabsContainer.innerHTML = data.expiryDates.map((date, idx) => {
                        return `
                            <button class="tab-btn px-3 py-1 px-4 py-1.5 rounded-sm text-xs font-medium ${idx === 0 ? 'bg-blue-500 text-white' : ''}" 
                                    data-expiry="${date}">
                                <span>${date}</span>
                            </button>`;
                    }).join('');

                    // Initialize data and display first expiry
                    window._lastOptionData = { allOptions: data.data, currentExpiry: data.expiryDates[0], expiryDates: data.expiryDates || [] };
                    displayOptionsForExpiry(data.data, data.expiryDates[0]);
                    displayBoxSpreadArbitrage(data.data, data.expiryDates[0]);

                    // Add tab click handlers
                    document.querySelectorAll('.tab-btn').forEach(btn => {
                        btn.addEventListener('click', function () {
                            document.querySelectorAll('.tab-btn').forEach(b =>
                                b.classList.remove('bg-blue-500', 'text-white')
                            );
                            this.classList.add('bg-blue-500', 'text-white');
                            displayOptionsForExpiry(data.data, this.dataset.expiry);
                            displayBoxSpreadArbitrage(data.data, this.dataset.expiry);
                            window._lastOptionData.currentExpiry = this.dataset.expiry;
                        });
                    });
                }

                function displayOptionsForExpiry(data, expiryDate) {
                    if (!data || !data.length) {
                        tableContainer.innerHTML = '<p>No data available</p>';
                        return;
                    }

                    let filtered = data.filter(item => item.expiryDate === expiryDate);

                    // Get future price for the expiry
                    futurePrice = window.futurePrices?.[expiryDate] || window.currentStockPrice || 0;

                    // Update future price display
                    if (futurePrice) {
                        futurePriceBoxContainer.innerHTML = `
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                            <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                <div class="text-xs text-gray-600 dark:text-gray-400">Future Price</div>
                                <div class="font-medium">₹${futurePrice.toFixed(2)}</div>
                            </div>
                        </div>
                    `;
                    }
                }

                // Initialize volume slider
                const volumeSlider = document.getElementById('volume-filter-slider');
                noUiSlider.create(volumeSlider, {
                    start: [100],
                    connect: [true, false],
                    range: { 'min': 0, 'max': 1000 },
                    step: 10,
                    format: {
                        to: value => Math.round(value),
                        from: value => parseInt(value)
                    }
                });

                // Update volume filter when slider changes
                volumeSlider.noUiSlider.on('update', function (values) {
                    const value = values[0];
                    // Update the label
                    document.querySelector('label[for="volume-filter-slider"] span').textContent = value;

                    // Update the display if we have data
                    if (window._lastOptionData?.allOptions) {
                        displayBoxSpreadArbitrage(window._lastOptionData.allOptions, window._lastOptionData.currentExpiry);
                    }
                });

                document.addEventListener('ltp-preference-changed', () => {
                    // Only respond to events from our specific checkbox or if no source is specified
                    if (event.detail.source && event.detail.source !== 'box-spread-arbitrage-single-use-bid-ask') return;

                    if (window._lastOptionData?.allOptions) {
                        displayBoxSpreadArbitrage(window._lastOptionData.allOptions, window._lastOptionData.currentExpiry);
                    }
                });

                // Helper function to convert PCP data to legs format
                function convertSpreadArbitrageToLegs(data, expiry, futurePrice) {
                    const useBidAsk = !window.useLTPForPrices;
                    const callPrice = useBidAsk ? (data.CE.bidprice || data.CE.askPrice) : data.CE.lastPrice;
                    const putPrice = useBidAsk ? (data.PE.bidprice || data.PE.askPrice) : data.PE.lastPrice;
                    const strike = data.strikePrice;

                    // Use future price for parity calculation
                    const computedDiff = callPrice - putPrice;
                    const expectedDiff = futurePrice - strike;
                    const parityDiff = computedDiff - expectedDiff;

                    // Determine position based on mispricing
                    const isCallOverpriced = parityDiff > 0;

                    return [
                        {
                            type: 'CE',
                            action: isCallOverpriced ? 'sell' : 'buy',
                            strike: data.strikePrice,
                            price: callPrice,
                            expiryDate: expiry,
                            lots: 1
                        },
                        {
                            type: 'PE',
                            action: isCallOverpriced ? 'buy' : 'sell',
                            strike: data.strikePrice,
                            price: putPrice,
                            expiryDate: expiry,
                            lots: 1
                        }
                    ];
                }

                // Display box spread arbitrage opportunities
                function displayBoxSpreadArbitrage(allOptions, expiryDate) {
                    let filtered = allOptions.filter(item => item.expiryDate === expiryDate);
                    filtered = filtered.filter(item => item.strikePrice && item.strikePrice != 0);

                    const useBidAsk = !window.useLTPForPrices;

                    // Filter valid options
                    filtered = filtered.filter(item => {
                        const callPrice = item.CE?.lastPrice;  // Use lastPrice for filtering
                        const putPrice = item.PE?.lastPrice;   // Use lastPrice for filtering
                        return callPrice != null && callPrice > 0 && putPrice != null && putPrice > 0 && item.CE && item.PE;
                    });

                    // Apply volume filter using slider value
                    const volumeThreshold = parseInt(volumeSlider.noUiSlider.get());
                    filtered = filtered.filter(item => {
                        const callVol = parseFloat(item.CE && item.CE.totalTradedVolume);
                        const putVol = parseFloat(item.PE && item.PE.totalTradedVolume);
                        return callVol >= volumeThreshold && putVol >= volumeThreshold;
                    });

                    if (!filtered.length) {
                        tableContainer.innerHTML = `<p class="text-red-500 text-sm">No valid strikes for ${expiryDate}</p>`;
                        return;
                    }

                    // Sort by strike price
                    filtered.sort((a, b) => a.strikePrice - b.strikePrice);

                    // Calculate spreads
                    const spreads = [];
                    for (let i = 0; i < filtered.length; i++) {
                        for (let j = i + 1; j < filtered.length; j++) {
                            const lowerOption = filtered[i];
                            const higherOption = filtered[j];

                            const strikeL = lowerOption.strikePrice;
                            const strikeH = higherOption.strikePrice;

                            // Static prices for display (always use lastPrice)
                            const callLowDisplay = lowerOption.CE.lastPrice || 0;
                            const callHighDisplay = higherOption.CE.lastPrice || 0;
                            const putLowDisplay = lowerOption.PE.lastPrice || 0;
                            const putHighDisplay = higherOption.PE.lastPrice || 0;

                            // Dynamic prices for calculation based on useBidAsk
                            let callLow, callHigh, putLow, putHigh;
                            if (useBidAsk) {
                                // Buy at ask, sell at bid
                                callLow = lowerOption.CE.askPrice || lowerOption.CE.lastPrice || 0;    // Buy call at lower strike
                                callHigh = higherOption.CE.bidprice || higherOption.CE.lastPrice || 0; // Sell call at higher strike
                                putLow = lowerOption.PE.bidprice || lowerOption.PE.lastPrice || 0;     // Sell put at lower strike
                                putHigh = higherOption.PE.askPrice || higherOption.PE.lastPrice || 0;  // Buy put at higher strike
                            } else {
                                // Use LTP for all
                                callLow = lowerOption.CE.lastPrice || 0;
                                callHigh = higherOption.CE.lastPrice || 0;
                                putLow = lowerOption.PE.lastPrice || 0;
                                putHigh = higherOption.PE.lastPrice || 0;
                            }

                            // Box Spread Calculations
                            const bullCallCost = callLow - callHigh;  // Buy low call, sell high call (net debit if positive)
                            const bearPutCost = putHigh - putLow;     // Buy high put, sell low put (net debit if positive)
                            const boxCost = bullCallCost + bearPutCost;  // Total cost to enter the box
                            const riskFreePayoff = strikeH - strikeL;    // Guaranteed payoff at expiration
                            const mispricing = riskFreePayoff - boxCost; // Arbitrage profit if positive

                            spreads.push({
                                lowerStrike: strikeL,
                                higherStrike: strikeH,
                                callLow: callLowDisplay,   // Display lastPrice
                                callHigh: callHighDisplay, // Display lastPrice
                                bullCallCost,
                                putLow: putLowDisplay,     // Display lastPrice
                                putHigh: putHighDisplay,   // Display lastPrice
                                bearPutCost,
                                boxCost,
                                riskFreePayoff,
                                mispricing
                            });
                        }
                    }

                    // Sort by mispricing (descending)
                    spreads.sort((a, b) => b.mispricing - a.mispricing);

                    // Limit to top opportunities (e.g., top 10)
                    const topSpreads = spreads.slice(0, 10);

                    if (!topSpreads.length) {
                        tableContainer.innerHTML = `
                            <div class="block text-sm text-neutral-800 font-medium mb-2">
                                Expiry: <span class="font-semibold">${expiryDate}</span>
                            </div>
                            <p class="text-gray-500 text-sm">No arbitrage opportunities found for ${expiryDate}</p>
                        `;
                        return;
                    }

                    // Build table
                    const rowsHtml = topSpreads.map(spread => `
                        <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700">
                            <td class="border p-1 text-center">${spread.lowerStrike}</td>
                            <td class="border p-1 text-center">${spread.higherStrike}</td>
                            <td class="border p-1 text-center">${spread.callLow.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.callHigh.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.bullCallCost.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.putLow.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.putHigh.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.bearPutCost.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.boxCost.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.riskFreePayoff.toFixed(2)}</td>
                            <td class="border p-1 text-center">${spread.mispricing.toFixed(2)}</td>
                            <td class="border p-1 text-center">
                                        <button
                                            onclick='window.PreviewModal.show({
                                                legs: ${JSON.stringify(convertSpreadToLegs(spread, expiryDate))},
                                                symbol: "${ticker}",
                                                spotPrice: ${futurePrice || window.currentStockPrice || 0},
                                                optionChain: ${JSON.stringify(window._lastOptionData.allOptions)},
                                                expiryDates: ${JSON.stringify(window._lastOptionData.expiryDates)},
                                                allowedTypes: ["CE", "PE", "FUT"],
                                            })' 
                                            title="Preview Strategy"
                                            class="p-1 hover:bg-gray-100 rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                 stroke-linecap="round" stroke-linejoin="round" 
                                                 class="lucide lucide-chart-candlestick text-gray-500">
                                                <path d="M9 5v4"></path>
                                                <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                                                <path d="M9 15v2"></path>
                                                <path d="M17 3v2"></path>
                                                <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                                                <path d="M17 13v3"></path>
                                                <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                                            </svg>
                                        </button>
                                    </td>
                        </tr>
                    `).join('');

                    tableContainer.innerHTML = `
                        <div class="block text-sm text-neutral-800 font-medium mb-2">
                            Expiry: <span class="font-semibold">${expiryDate}</span>
                        </div>
                        <div class="overflow-auto xl:max-h-[calc(100vh_-_213px)] custom-scroll">
                            <table class="w-full border-collapse border text-xs" id="box-spread-table">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="border p-1 text-center text-nowrap" data-col="lowerStrike">Lower Strike</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="higherStrike">Higher Strike</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="callLow">Call Price (Lower)</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="callHigh">Call Price (Higher)</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="bullCallCost">Bull Call Cost</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="putLow">Put Price (Lower)</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="putHigh">Put Price (Higher)</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="bearPutCost">Bear Put Cost</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="boxCost">Total Box Cost</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="riskFreePayoff">Risk Free Payoff</th>
                                        <th class="border p-1 text-center text-nowrap" data-col="mispricing">Mispricing</th>
                                        <th class="border p-1 text-center text-nowrap" data-no-sort>Preview</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${rowsHtml}
                                </tbody>
                            </table>
                        </div>
                    `;

                    // Add sorting to the table
                    window.addSorting('box-spread-table');
                }

            });

            // Tab switching functionality
            document.addEventListener('DOMContentLoaded', () => {
                const searchTab = document.getElementById("search-tab");
                const allStocksTab = document.getElementById("all-stocks-tab");
                const searchContent = document.getElementById("search-content");
                const allStocksContent = document.getElementById("all-stocks-content");

                function switchTab(activeTab, activeContent, inactiveTab, inactiveContent) {
                    activeTab.classList.remove(
                        "text-gray-500",
                        "border-transparent",
                        "hover:text-gray-700",
                        "hover:border-gray-300"
                    );
                    activeTab.classList.add(
                        "bg-white",
                        "text-blue-600",
                        "rounded-md",
                        "shadow-md"
                    );

                    inactiveTab.classList.add(
                        "text-gray-500",
                        "border-transparent",
                        "hover:text-gray-700",
                        "hover:border-gray-300"
                    );
                    inactiveTab.classList.remove(
                        "bg-white",
                        "text-blue-600",
                        "rounded-md",
                        "shadow-md"
                    );

                    activeContent.classList.remove("hidden");
                    inactiveContent.classList.add("hidden");
                }

                searchTab.addEventListener("click", () => {
                    switchTab(searchTab, searchContent, allStocksTab, allStocksContent);
                });

                allStocksTab.addEventListener("click", () => {
                    switchTab(allStocksTab, allStocksContent, searchTab, searchContent);
                });
            });

        </script>

        {% include 'blocks/lot-sizes.html' %}


    </div>
</body>

</html>