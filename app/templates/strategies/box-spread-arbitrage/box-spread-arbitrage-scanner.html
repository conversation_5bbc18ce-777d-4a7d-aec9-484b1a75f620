{% include 'blocks/timestamp.html' %}

<!-- Box Spread Arbitrage Scanner -->
<main class="h-full">
    <div>
        <div>
            <!-- Left Panel -->
            <div class=" w-full">
                <!-- Stock Category Selector -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                    <div>
                        <div class="flex flex-wrap gap-4">
                            <label class="flex items-center">
                                <input type="radio" name="scanner-stock-category" value="index" checked class="mr-2" />
                                Indices
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="scanner-stock-category" value="nifty" class="mr-2" />
                                Nifty Stocks
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="scanner-stock-category" value="fo" class="mr-2" />
                                F&amp;O Stocks
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Ticker Selection Section -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col h-[350px] lg:max-h-[calc(60vh)]">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="font-semibold">Select Stocks</h2>
                        <div class="flex gap-2 items-center">
                            <button id="scanner-select-all"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button id="scanner-unselect-all"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Unselect All
                            </button>
                        </div>
                    </div>
                    <div id="scanner-ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll"></div>
                    <div class="pt-4 border-t mt-auto">
                        <div class="flex gap-2 justify-between">

                            <button id="scanner-scan-button"
                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">
                                Scan Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="mt-3">
                <!-- Filter Section -->
                <div id="scanner-results-filter-section"
                    class="bg-white dark:bg-neutral-800 rounded-lg rounded-md p-4 mb-4 hidden">
                    <div class="md:flex justify-between mb-3">
                        <h3 class="font-semibold">Filter Results</h3>
                        {% with id='box-spread-arbitrage-scanner-use-bid-ask' %}
                        {% include 'blocks/use-ltp-checkbox.html' %}
                        {% endwith %}
                    </div>

                    <div class="grid md:grid-cols-4 md:gap-12 gap-4 mb-4">
                        <!-- Volume Threshold Filter -->
                        <div>
                            <label for="volume-threshold-slider" class="block font-medium mb-2 text-sm">
                                Volume Threshold (<span>20</span>)
                            </label>
                            <div id="volume-threshold-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>100</span>
                            </div>
                        </div>

                        <!-- Bull Call Cost Filter -->
                        <div>
                            <label for="bull-call-cost-slider" class="block font-medium mb-2 text-sm">
                                Bull Call Cost (<span>0</span>)
                            </label>
                            <div id="bull-call-cost-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <!-- Box Premium Filter -->
                        <div>
                            <label for="premium-slider" class="block font-medium mb-2 text-sm">
                                Box Premium (<span>0</span>)
                            </label>
                            <div id="premium-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <!-- Risk-Free Payoff Filter -->
                        <div>
                            <label for="payoff-slider" class="block font-medium mb-2 text-sm">
                                Min Payoff (<span>0</span>)
                            </label>
                            <div id="payoff-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <div>
                            <label for="call-bid-qty" class="block font-medium mb-2 text-sm">
                                Call Bid Qty (<span>0</span>)
                            </label>
                            <div id="call-bid-qty" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <div>
                            <label for="call-ask-qty" class="block font-medium mb-2 text-sm">
                                Call Ask Qty (<span>0</span>)
                            </label>
                            <div id="call-ask-qty" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <div>
                            <label for="put-bid-qty" class="block font-medium mb-2 text-sm">
                                Put Bid Qty (<span>0</span>)
                            </label>
                            <div id="put-bid-qty" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>

                        <div>
                            <label for="put-ask-qty" class="block font-medium mb-2 text-sm">
                                Put Ask Qty (<span>0</span>)
                            </label>
                            <div id="put-ask-qty" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span>0</span>
                                <span>1000</span>
                            </div>
                        </div>
                    </div>

                    <!-- Expiry Tabs --> <!-- Expiry Tabs -->
                    <div id="scanner-expiry-tabs" class="custom-scroll flex flex-wrap gap-1 my-3 overflow-x-auto">
                    </div>
                </div>

                <!-- Summary Table -->
                <div id="scanner-summary-table" class="bg-white dark:bg-neutral-800 rounded-lg p-4"></div>

                <!-- Scanner Results -->
                <div id="scanner-results-container"
                    class="md:max-h-[calc(100vh_-_390px)] custom-scroll overflow-y-auto">
                </div>
            </div>
        </div>
    </div>
</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css" />

<style>
    /* Make sliders show pointer cursor on hover */
    .noUi-target,
    .noUi-handle,
    .noUi-connect {
        cursor: pointer !important;
    }
</style>

{% include 'blocks/stock-symbols.html' %}

<script>
    document.addEventListener('DOMContentLoaded', () => {
        console.log("Box Spread Arbitrage Scanner loaded.");

        let currentTickers = indexTickers;
        let currentCategory = "index";
        const scannedData = {};
        let scannedTickers = [];
        let selectedExpiry = null; // first ticker's expiry dates

        let summaryResults = [];

        // DOM elements
        const tickerSelectionContainer = document.getElementById('scanner-ticker-selection');
        const selectAllBtn = document.getElementById('scanner-select-all');
        const unselectAllBtn = document.getElementById('scanner-unselect-all');
        const scanButton = document.getElementById('scanner-scan-button');
        const expiryTabsContainer = document.getElementById('scanner-expiry-tabs');
        const resultsContainer = document.getElementById('scanner-results-container');
        const summaryTableContainer = document.getElementById('scanner-summary-table');

        // First, declare all slider variables at the top with other DOM elements
        const volumeSlider = document.getElementById('volume-threshold-slider');
        const bullCallSlider = document.getElementById('bull-call-cost-slider');
        const premiumSlider = document.getElementById('premium-slider');
        const payoffSlider = document.getElementById('payoff-slider');
        const callBidQtySlider = document.getElementById('call-bid-qty');
        const callAskQtySlider = document.getElementById('call-ask-qty');
        const putBidQtySlider = document.getElementById('put-bid-qty');
        const putAskQtySlider = document.getElementById('put-ask-qty');

        const volumeMinValue = document.getElementById('volume-min-value');
        const volumeMaxValue = document.getElementById('volume-max-value');
        const bullCallMinValue = document.getElementById('bull-call-min-value');
        const bullCallMaxValue = document.getElementById('bull-call-max-value');
        const premiumMinValue = document.getElementById('premium-min-value');
        const premiumMaxValue = document.getElementById('premium-max-value');
        const payoffMinValue = document.getElementById('payoff-min-value');
        const payoffMaxValue = document.getElementById('payoff-max-value');

        function buildTickerSelection(tickerArray) {
            let html = '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
            tickerArray.forEach(ticker => {
                html += `
          <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
            <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
            <span>${ticker}</span>
          </label>
        `;
            });
            html += '</div>';
            tickerSelectionContainer.innerHTML = html;
        }
        buildTickerSelection(currentTickers);

        const stockCategoryRadios = document.getElementsByName('scanner-stock-category');
        stockCategoryRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                if (this.checked) {
                    currentCategory = this.value;
                    if (currentCategory === "nifty") {
                        currentTickers = niftyTickers;
                    } else if (currentCategory === "fo") {
                        currentTickers = foTickers;
                    } else if (currentCategory === "index") {
                        currentTickers = indexTickers;
                    }
                    buildTickerSelection(currentTickers);
                }
            });
        });

        selectAllBtn.addEventListener('click', () => {
            document.querySelectorAll('.ticker-checkbox').forEach(cb => cb.checked = true);
        });
        unselectAllBtn.addEventListener('click', () => {
            document.querySelectorAll('.ticker-checkbox').forEach(cb => cb.checked = false);
        });

        async function fetchFutures(ticker) {
            try {
                const res = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${ticker}`);
                return await res.json();
            } catch (error) {
                console.error("Error fetching futures for " + ticker, error);
                return null;
            }
        }

        async function processTicker(ticker) {
            let panel = document.getElementById('scanner-ticker-' + ticker);
            if (!panel) {
                panel = document.createElement('div');
                panel.id = 'scanner-ticker-' + ticker;
                panel.className = 'mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700';
                panel.innerHTML = `<h2 class="text-base font-medium">${ticker} - Scanning...</h2>
                             <div id="scanner-ticker-content-${ticker}"></div>`;
                resultsContainer.appendChild(panel);
            }
            const contentContainer = document.getElementById('scanner-ticker-content-' + ticker);

            let records = null;
            try {
                const optionsUrl = (currentCategory === "index") ?
                    `https://iam.theaibull.com/v1/wg7ttpouv7/indices/options/${ticker}` :
                    `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`;

                const response = await fetch(optionsUrl);
                const text = await response.text(); // Get response as text first

                // Replace NaN with null in the response text
                const sanitizedText = text.replace(/:\s*NaN/g, ': null');

                try {
                    const optionsData = JSON.parse(sanitizedText);
                    records = optionsData.records;
                } catch (parseError) {
                    console.error('Error parsing JSON for ' + ticker, parseError);
                    contentContainer.innerHTML = `
                        <div class="p-4 bg-red-50 text-red-600 rounded-lg">
                            <p class="font-medium">Error: Invalid data received for ${ticker}</p>
                            <p class="text-sm mt-1">Please try again later or contact support if the issue persists.</p>
                        </div>`;
                    return;
                }
            } catch (err) {
                console.error('Error fetching options for ' + ticker, err);
                contentContainer.innerHTML = `
                    <div class="p-4 bg-red-50 text-red-600 rounded-lg">
                        <p class="font-medium">Failed to fetch option chain for ${ticker}</p>
                        <p class="text-sm mt-1">Please check your internet connection and try again.</p>
                    </div>`;
                return;
            }

            const expiryDates = records.expiryDates || [];
            const optionData = records.data || [];

            let underlyingValue = null;
            if (optionData && optionData.length > 0) {
                for (const record of optionData) {
                    if (record.CE && record.CE.underlyingValue) {
                        underlyingValue = record.CE.underlyingValue;
                        break;
                    }
                    if (record.PE && record.PE.underlyingValue) {
                        underlyingValue = record.PE.underlyingValue;
                        break;
                    }
                }
            }

            if (currentCategory !== "index") {
                const futures = await fetchFutures(ticker);
                scannedData[ticker] = { underlyingValue, expiryDates, optionData, futurePrices: futures };
                panel.querySelector('h2').innerHTML = `${ticker} - Price: ${underlyingValue ? underlyingValue.toFixed(2) : 'N/A'}`;
            } else {
                scannedData[ticker] = { underlyingValue, expiryDates, optionData };
                panel.querySelector('h2').innerHTML = `${ticker} (Index) - Value: ${underlyingValue ? underlyingValue.toFixed(2) : 'N/A'}`;
            }
        }

        function buildExpiryTabs(expiryDates) {
            const tabsHtml = expiryDates.map((date, idx) => `
                <button 
                    class="tab-btn px-4 py-1.5 rounded-sm text-xs text-wrap font-medium border
                           ${idx === 0 ? 'bg-blue-500 text-white' : 'bg-white dark:bg-neutral-800 dark:hover:bg-neutral-700'}"
                    data-expiry="${date}"
                >
                    ${date}
                </button>
            `).join('');

            expiryTabsContainer.innerHTML = tabsHtml;

            document.getElementById('scanner-results-filter-section').classList.remove('hidden');

            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function () {
                    document.querySelectorAll('.tab-btn').forEach(b => {
                        b.classList.remove('bg-blue-500', 'text-white');
                        b.classList.add('bg-white', 'dark:bg-neutral-800');
                    });
                    this.classList.remove('bg-white', 'dark:bg-neutral-800');
                    this.classList.add('bg-blue-500', 'text-white');

                    selectedExpiry = this.dataset.expiry;
                    scannedTickers.forEach(ticker => {
                        renderBoxSpreadData(ticker, selectedExpiry);
                    });
                    buildSummaryTable();
                });
            });
        }

        // Helper: Convert a spread object to an array of leg objectsf.
        window.convertSpreadToLegs = function (spread, expiry) {
            return [
                { type: 'CE', strike: spread.lowerStrike, action: 'buy', price: spread.callLow, lots: 1, expiryDate: expiry },
                { type: 'CE', strike: spread.higherStrike, action: 'sell', price: spread.callHigh, lots: 1, expiryDate: expiry },
                { type: 'PE', strike: spread.lowerStrike, action: 'sell', price: spread.putLow, lots: 1, expiryDate: expiry },
                { type: 'PE', strike: spread.higherStrike, action: 'buy', price: spread.putHigh, lots: 1, expiryDate: expiry }
            ];
        }

        // Add event listener for the checkbox
        document.addEventListener('ltp-preference-changed', () => {
            // Only respond to events from our specific checkbox or if no source is specified
            if (event.detail.source && event.detail.source !== 'box-spread-arbitrage-scanner-use-bid-ask') return;

            if (selectedExpiry) {
                scannedTickers.forEach(ticker => {
                    renderBoxSpreadData(ticker, selectedExpiry);
                });
                buildSummaryTable();
            }
        });

        // Global variable for preview legs so that interactive controls work.
        window.previewLegs = [];

        // ------------------------------------------------------------------
        // Render Box Spread Data for one ticker at a given expiry.
        // ------------------------------------------------------------------
        function renderBoxSpreadData(ticker, expiry) {
            const tickerData = scannedData[ticker];
            if (!tickerData) return;
            const allOptions = tickerData.optionData;
            const volumeThreshold = parseInt(volumeSlider.noUiSlider.get());
            let filtered = allOptions.filter(item =>
                item.expiryDate === expiry &&
                item.strikePrice && item.strikePrice != 0 &&
                item.CE && item.PE
            );

            const useBidAsk = !window.useLTPForPrices;

            // Filter based on price availability
            filtered = filtered.filter(item => {
                const callPrice = item.CE.lastPrice;
                const putPrice = item.PE.lastPrice;
                return callPrice != null && callPrice > 0 && putPrice != null && putPrice > 0;
            });

            let futurePrice = null;
            if (currentCategory !== "index") {
                const futures = tickerData.futurePrices;
                if (!futures || !futures[expiry]) {
                    setTimeout(() => renderBoxSpreadData(ticker, expiry), 500);
                    return;
                }
                futurePrice = futures[expiry];
            }

            let spreads = [];
            for (let i = 0; i < filtered.length; i++) {
                for (let j = i + 1; j < filtered.length; j++) {
                    const callVol = parseFloat(filtered[i].CE.totalTradedVolume);
                    const putVol = parseFloat(filtered[j].PE.totalTradedVolume);
                    const lowerOption = filtered[i];
                    const higherOption = filtered[j];
                    const strikeL = lowerOption.strikePrice;
                    const strikeH = higherOption.strikePrice;

                    // Static prices for display (always use lastPrice)
                    const callLowDisplay = lowerOption.CE.lastPrice || 0;
                    const callHighDisplay = higherOption.CE.lastPrice || 0;
                    const putLowDisplay = lowerOption.PE.lastPrice || 0;
                    const putHighDisplay = higherOption.PE.lastPrice || 0;

                    // Dynamic prices for calculation based on useBidAsk
                    let callLow, callHigh, putLow, putHigh;
                    if (useBidAsk) {
                        // Buy at ask, sell at bid
                        callLow = lowerOption.CE.askPrice || lowerOption.CE.lastPrice || 0;  // Buy call at lower strike
                        callHigh = higherOption.CE.bidprice || higherOption.CE.lastPrice || 0;  // Sell call at higher strike
                        putLow = lowerOption.PE.bidprice || lowerOption.PE.lastPrice || 0;     // Sell put at lower strike
                        putHigh = higherOption.PE.askPrice || higherOption.PE.lastPrice || 0;  // Buy put at higher strike
                    } else {
                        // Use LTP for all
                        callLow = lowerOption.CE.lastPrice || 0;
                        callHigh = higherOption.CE.lastPrice || 0;
                        putLow = lowerOption.PE.lastPrice || 0;
                        putHigh = higherOption.PE.lastPrice || 0;
                    }

                    // Box Spread Calculations
                    const bullCallCost = callLow - callHigh;  // Buy low call, sell high call (net debit if positive)
                    const bearPutCost = putHigh - putLow;     // Buy high put, sell low put (net debit if positive)
                    const boxCost = bullCallCost + bearPutCost;  // Total cost to enter the box (net debit or credit)
                    const riskFreePayoff = strikeH - strikeL;    // Guaranteed payoff at expiration
                    const mispricing = riskFreePayoff - boxCost; // Profit if positive (arbitrage opportunity)

                    if (mispricing > 0) {  // Only include spreads with arbitrage potential
                        spreads.push({
                            lowerStrike: strikeL,
                            higherStrike: strikeH,
                            callLow: callLowDisplay,   // Display LTP
                            callHigh: callHighDisplay, // Display LTP
                            callLowVol: lowerOption.CE.totalTradedVolume,
                            callHighVol: higherOption.CE.totalTradedVolume,
                            bullCallCost: bullCallCost,
                            putLow: putLowDisplay,     // Display LTP
                            putHigh: putHighDisplay,   // Display LTP
                            putLowVol: lowerOption.PE.totalTradedVolume,
                            putHighVol: higherOption.PE.totalTradedVolume,
                            bearPutCost: bearPutCost,
                            boxCost: boxCost,
                            riskFreePayoff: riskFreePayoff,
                            mispricing: mispricing,
                            callVol: callVol,
                            putVol: putVol
                        });
                    }
                }
            }
            spreads.sort((a, b) => b.mispricing - a.mispricing);
            spreads = filterSpreads(spreads);  // Apply all filters
            const top3 = spreads.slice(0, 3);
            scannedData[ticker].topBoxSpreads = top3;

            // Handle empty state
            if (spreads.length === 0) {
                document.getElementById('scanner-ticker-content-' + ticker).innerHTML = `
                    <div class="mb-2 text-gray-600">
                        Expiry: <span class="font-semibold">${expiry}</span>
                        ${currentCategory !== "index" ? `<span class="block text-sm text-gray-500">Future Price: ${futurePrice.toFixed(2)}</span>` : ''}
                    </div>
                    <div class="p-8 text-center bg-gray-50 rounded-lg border border-dashed">
                        <p class="text-gray-500">No box spreads found matching the filter criteria.</p>
                        <p class="text-sm text-gray-400 mt-2">Try adjusting the filters or selecting a different expiry date.</p>
                    </div>
                `;
                return;
            }

            const tableId = `scanner-box-table-${ticker.replace(/\W/g, '-')}`;
            let html = `
          <div class="my-2 text-gray-600">
            <div class="mb-2 text-gray-900 dark:text-gray-300 text-xs px-2 py-1 bg-red-50 inline-flex font-medium">
            Expiry : <span class="font-semibold">${expiry}</span>
            </div>
            ${currentCategory !== "index" ? `<div class="mb-2 text-gray-900 font-medium dark:text-gray-300 text-xs px-2 py-1 bg-purple-100 inline-flex"><span>Future Price: ${futurePrice.toFixed(2)}</span></div>` : ''}
          </div>
          <div class="overflow-x-auto">
            <table class="w-full border-collapse border dark:border-neutral-700 text-xs" id="${tableId}">
              <thead>
                <tr class="bg-gray-50 dark:bg-neutral-700 text-neutral-800">
                  <th class="border p-1 text-center text-nowrap" data-col="lowerStrike">Lower<br>Strike</th>
                  <th class="border p-1 text-center text-nowrap" data-col="higherStrike">Higher<br>Strike</th>
                  <th class="border p-1 text-center text-nowrap" data-col="callLow">Call @ Lower<br>Price (Vol)</th>
                  <th class="border p-1 text-center text-nowrap" data-col="callHigh">Call @ Higher<br>Price (Vol)</th>
                  <th class="border p-1 text-center text-nowrap" data-col="bullCallCost">Bull Call<br>Spread Cost</th>
                  <th class="border p-1 text-center text-nowrap" data-col="putLow">Put @ Lower<br>Price (Vol)</th>
                  <th class="border p-1 text-center text-nowrap" data-col="putHigh">Put @ Higher<br>Price (Vol)</th>
                  <th class="border p-1 text-center text-nowrap" data-col="bearPutCost">Bear Put<br>Spread Cost</th>
                  <th class="border p-1 text-center text-nowrap" data-col="boxCost">Box Spread<br>Premium</th>
                  <th class="border p-1 text-center text-nowrap" data-col="riskFreePayoff">Risk-Free<br>Payoff</th>
                  <th class="border p-1 text-center text-nowrap" data-col="mispricing">Mispricing</th>
                  <th class="border p-1 text-center text-nowrap" data-no-sort>Strategy<br>Preview</th>
                </tr>
              </thead>
              <tbody>
      `;
            top3.forEach(spread => {
                const premiumText = spread.boxCost >= 0
                    ? "Debit " + spread.boxCost.toFixed(2)
                    : "Credit " + Math.abs(spread.boxCost).toFixed(2);
                html += `
            <tr class="hover:bg-gray-50">
              <td class="border p-1 text-center">${spread.lowerStrike}</td>
              <td class="border p-1 text-center">${spread.higherStrike}</td>
              <td class="border p-1 text-center">${spread.callLow.toFixed(2)} (${spread.callLowVol})</td>
              <td class="border p-1 text-center">${spread.callHigh.toFixed(2)} (${spread.callHighVol})</td>
              <td class="border p-1 text-center">${spread.bullCallCost.toFixed(2)}</td>
              <td class="border p-1 text-center">${spread.putLow.toFixed(2)} (${spread.putLowVol})</td>
              <td class="border p-1 text-center">${spread.putHigh.toFixed(2)} (${spread.putHighVol})</td>
              <td class="border p-1 text-center">${spread.bearPutCost.toFixed(2)}</td>
              <td class="border p-1 text-center">${premiumText}</td>
              <td class="border p-1 text-center">${spread.riskFreePayoff.toFixed(2)}</td>
              <td class="border p-1 text-center">${spread.mispricing.toFixed(2)}</td>
              <td class="border p-1 text-center">
                <button 
                    onclick='window.PreviewModal.show({
                        legs: ${JSON.stringify(convertSpreadToLegs(spread, expiry))},
                        symbol: "${ticker}",
                        spotPrice: ${tickerData.underlyingValue || 0},
                        optionChain: ${JSON.stringify(tickerData.optionData)},
                        expiryDates: ${JSON.stringify(tickerData.expiryDates)},
                        allowedTypes: ["CE", "PE", "FUT"],
                    })' 
                    title="Preview Box Spread">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                         stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-candlestick text-gray-500">
                        <path d="M9 5v4"></path>
                        <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                        <path d="M9 15v2"></path>
                        <path d="M17 3v2"></path>
                        <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                        <path d="M17 13v3"></path>
                        <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                    </svg>
                </button>
              </td>
            </tr>
          `;
            });
            html += `
              </tbody>
            </table>
          </div>
      `;
            document.getElementById('scanner-ticker-content-' + ticker).innerHTML = html;

            // Add sorting to the table
            setTimeout(() => window.addSorting(tableId), 0);
        }

        function buildSummaryTable() {
            summaryResults = [];
            scannedTickers.forEach(ticker => {
                const data = scannedData[ticker];
                if (data.topBoxSpreads && data.topBoxSpreads.length) {
                    data.topBoxSpreads.forEach(spread => {
                        summaryResults.push({
                            ticker,
                            lowerStrike: spread.lowerStrike,
                            higherStrike: spread.higherStrike,
                            boxCost: spread.boxCost,
                            riskFreePayoff: spread.riskFreePayoff,
                            mispricing: spread.mispricing,
                            spreadData: spread  // Store full spread data for preview
                        });
                    });
                }
            });

            // Handle empty state for summary table
            if (summaryResults.length === 0) {
                summaryTableContainer.innerHTML = `
                    <div class="p-8 text-center bg-gray-50 rounded-lg border border-dashed mt-6">
                        <h2 class="text-xl font-bold mb-2 text-gray-700">No Results Found</h2>
                        <p class="text-gray-500">No box spreads found matching the current filter criteria across any symbols.</p>
                        <p class="text-sm text-gray-400 mt-2">Try adjusting the filters or selecting different symbols.</p>
                    </div>
                `;
                return;
            }

            summaryResults.sort((a, b) => b.riskFreePayoff - a.riskFreePayoff);
            let summaryHtml = `
          <h2 class="text-lg font-semibold mb-2 text-left">Summary: Top Box Spreads Across Stocks</h2>
          <div class="overflow-x-auto">
            <table class="w-full border-collapse border text-xs" id="scanner-summary-table-inner">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border p-1 text-center" data-col="ticker">Symbol</th>
                  <th class="border p-1 text-center" data-col="lowerStrike">Lower Strike</th>
                  <th class="border p-1 text-center" data-col="higherStrike">Higher Strike</th>
                  <th class="border p-1 text-center" data-col="boxCost">Box Spread Premium</th>
                  <th class="border p-1 text-center" data-col="riskFreePayoff">Risk-Free Payoff</th>
                  <th class="border p-1 text-center" data-col="mispricing">Mispricing</th>
                  <th class="border p-1 text-center" data-no-sort>Strategy Preview</th>
                </tr>
              </thead>
              <tbody>
      `;
            summaryResults.forEach(item => {
                const premiumText = item.boxCost >= 0
                    ? "Debit " + item.boxCost.toFixed(2)
                    : "Credit " + Math.abs(item.boxCost).toFixed(2);
                summaryHtml += `
            <tr class="hover:bg-gray-50">
              <td class="border p-1 text-center">${item.ticker}</td>
              <td class="border p-1 text-center">${item.lowerStrike}</td>
              <td class="border p-1 text-center">${item.higherStrike}</td>
              <td class="border p-1 text-center">${premiumText}</td>
              <td class="border p-1 text-center">${item.riskFreePayoff.toFixed(2)}</td>
              <td class="border p-1 text-center">${item.mispricing.toFixed(2)}</td>
              <td class="border p-1 text-center">
                    <button 
                        onclick='window.PreviewModal.show({
                            legs: ${JSON.stringify(convertSpreadToLegs(item.spreadData, selectedExpiry))},
                            symbol: "${item.ticker}",
                            spotPrice: ${scannedData[item.ticker].underlyingValue || 0},
                            optionChain: ${JSON.stringify(scannedData[item.ticker].optionData)},
                            expiryDates: ${JSON.stringify(scannedData[item.ticker].expiryDates)},
                            allowedTypes: ["CE", "PE", "FUT"]
                        })' 
                        title="Preview Box Spread">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-candlestick text-gray-500">
                            <path d="M9 5v4"></path>
                            <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                            <path d="M9 15v2"></path>
                            <path d="M17 3v2"></path>
                            <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                            <path d="M17 13v3"></path>
                            <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                        </svg>
                    </button>
                </td>
            </tr>
          `;
            });
            summaryHtml += `
              </tbody>
            </table>
          </div>
      `;
            summaryTableContainer.innerHTML = summaryHtml;

            // Add sorting to the summary table
            setTimeout(() => window.addSorting('scanner-summary-table-inner'), 0);
        }

        scanButton.addEventListener('click', () => {
            // Hide the filter section when starting new scan
            document.getElementById('scanner-results-filter-section').classList.add('hidden');

            // Clear previous results
            resultsContainer.innerHTML = '';
            expiryTabsContainer.innerHTML = '';
            summaryTableContainer.innerHTML = '';
            for (let key in scannedData) { delete scannedData[key]; }
            scannedTickers = [];
            summaryResults = [];

            const selected = Array.from(document.querySelectorAll('.ticker-checkbox:checked')).map(cb => cb.value);
            if (selected.length === 0) {
                alert('Please select at least one ticker.');
                return;
            }
            scannedTickers = selected;
            Promise.all(scannedTickers.map(ticker => processTicker(ticker)))
                .then(() => {
                    const firstTicker = scannedTickers[0];
                    if (scannedData[firstTicker] && scannedData[firstTicker].expiryDates.length > 0) {
                        // Show filter section only after successful scan
                        document.getElementById('scanner-results-filter-section').classList.remove('hidden');
                        buildExpiryTabs(scannedData[firstTicker].expiryDates);
                        selectedExpiry = scannedData[firstTicker].expiryDates[0];
                        scannedTickers.forEach(ticker => {
                            renderBoxSpreadData(ticker, selectedExpiry);
                        });
                        setTimeout(buildSummaryTable, 1000);
                    } else {
                        expiryTabsContainer.innerHTML = `<p class="text-red-500">No expiry data for ${firstTicker}</p>`;
                    }
                })
                .catch(err => {
                    console.error('Error processing tickers', err);
                });
        });

        // Remove the duplicate slider initializations and event listeners
        function initializeSliders() {
            // Volume Threshold slider
            noUiSlider.create(volumeSlider, {
                start: [20],
                connect: [true, false],
                range: { 'min': 0, 'max': 100 },
                step: 1,
                format: {
                    to: value => Math.round(value),
                    from: value => parseInt(value)
                }
            });

            // Bull Call Cost slider
            noUiSlider.create(bullCallSlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            // Box Premium slider
            noUiSlider.create(premiumSlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            // Risk-Free Payoff slider
            noUiSlider.create(payoffSlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            noUiSlider.create(callBidQtySlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            noUiSlider.create(callAskQtySlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            noUiSlider.create(putBidQtySlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            noUiSlider.create(putAskQtySlider, {
                start: [0],
                connect: [true, false],
                range: { 'min': 0, 'max': 1000 },
                step: 1
            });

            // Update function for all filters
            function updateFilters() {
                if (selectedExpiry) {
                    // Update labels with current values
                    const volumeValue = volumeSlider.noUiSlider.get();
                    const bullCallValue = bullCallSlider.noUiSlider.get();
                    const premiumValue = premiumSlider.noUiSlider.get();
                    const payoffValue = payoffSlider.noUiSlider.get();
                    const callBidQtyValue = callBidQtySlider.noUiSlider.get();
                    const callAskQtyValue = callAskQtySlider.noUiSlider.get();
                    const putBidQtyValue = putBidQtySlider.noUiSlider.get();
                    const putAskQtyValue = putAskQtySlider.noUiSlider.get();

                    // Update the label values (the ones in parentheses)
                    document.querySelector('label[for="volume-threshold-slider"] span').textContent = volumeValue;
                    document.querySelector('label[for="bull-call-cost-slider"] span').textContent = bullCallValue;
                    document.querySelector('label[for="premium-slider"] span').textContent = premiumValue;
                    document.querySelector('label[for="payoff-slider"] span').textContent = payoffValue;
                    document.querySelector('label[for="call-bid-qty"] span').textContent = callBidQtyValue;
                    document.querySelector('label[for="call-ask-qty"] span').textContent = callAskQtyValue;
                    document.querySelector('label[for="put-bid-qty"] span').textContent = putBidQtyValue;
                    document.querySelector('label[for="put-ask-qty"] span').textContent = putAskQtyValue;

                    // Update the min/max values below sliders
                    document.querySelector('#volume-threshold-slider + div span:first-child').textContent = '0';
                    document.querySelector('#volume-threshold-slider + div span:last-child').textContent = '100';

                    document.querySelector('#bull-call-cost-slider + div span:first-child').textContent = '0';
                    document.querySelector('#bull-call-cost-slider + div span:last-child').textContent = '1000';

                    document.querySelector('#premium-slider + div span:first-child').textContent = '0';
                    document.querySelector('#premium-slider + div span:last-child').textContent = '1000';

                    document.querySelector('#payoff-slider + div span:first-child').textContent = '0';
                    document.querySelector('#payoff-slider + div span:last-child').textContent = '1000';

                    document.querySelector('#call-bid-qty + div span:first-child').textContent = '0';
                    document.querySelector('#call-bid-qty + div span:last-child').textContent = '1000';

                    document.querySelector('#call-ask-qty + div span:first-child').textContent = '0';
                    document.querySelector('#call-ask-qty + div span:last-child').textContent = '1000';

                    document.querySelector('#put-bid-qty + div span:first-child').textContent = '0';
                    document.querySelector('#put-bid-qty + div span:last-child').textContent = '1000';

                    document.querySelector('#put-ask-qty + div span:first-child').textContent = '0';
                    document.querySelector('#put-ask-qty + div span:last-child').textContent = '1000';
                    // Update the results
                    scannedTickers.forEach(ticker => {
                        renderBoxSpreadData(ticker, selectedExpiry);
                    });
                    buildSummaryTable();
                }
            }

            // Add listeners to all sliders
            [volumeSlider, bullCallSlider, premiumSlider, payoffSlider, callBidQtySlider, callAskQtySlider, putBidQtySlider, putAskQtySlider].forEach(slider => {
                slider.noUiSlider.on('update', updateFilters);
            });
        }

        // Call initializeSliders after DOM content is loaded
        initializeSliders();

        // Update the filtering logic in renderBoxSpreadData
        function filterSpreads(spreads) {
            const volumeThreshold = parseInt(document.getElementById('volume-threshold-slider').noUiSlider.get());
            const bullCallThreshold = parseFloat(document.getElementById('bull-call-cost-slider').noUiSlider.get());
            const premiumThreshold = parseFloat(document.getElementById('premium-slider').noUiSlider.get());
            const payoffThreshold = parseFloat(document.getElementById('payoff-slider').noUiSlider.get());
            const callBidQtyThreshold = parseFloat(document.getElementById('call-bid-qty').noUiSlider.get());
            const callAskQtyThreshold = parseFloat(document.getElementById('call-ask-qty').noUiSlider.get());
            const putBidQtyThreshold = parseFloat(document.getElementById('put-bid-qty').noUiSlider.get());
            const putAskQtyThreshold = parseFloat(document.getElementById('put-ask-qty').noUiSlider.get());

            return spreads.filter(spread => {
                return (
                    spread.callVol >= volumeThreshold && spread.putVol >= volumeThreshold &&
                    Math.abs(spread.bullCallCost) >= bullCallThreshold &&
                    Math.abs(spread.boxCost) >= premiumThreshold &&
                    spread.riskFreePayoff >= payoffThreshold &&
                    spread.callLowVol >= callBidQtyThreshold &&
                    spread.callLowVol >= callAskQtyThreshold &&
                    spread.putLowVol >= putBidQtyThreshold &&
                    spread.putLowVol >= putAskQtyThreshold
                );
            });
        }
    });
</script>