<!DOCTYPE html>
<html lang="en">

<head>

    <title>Stocks vs Exchange Arbitrage Opportunities Between NSE and BSE | AI Bull</title>
    <meta name="description"
        content="Discover arbitrage opportunities between NSE and BSE by analyzing price discrepancies between stocks listed on both exchanges. Maximize profits by exploiting market inefficiencies." />
    <link rel="canonical" href="https://theaibull.com/strategies/exchanges-arbitrage" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/exchanges-arbitrage" />
    <meta property="og:title"
        content="Stocks vs Exchange Arbitrage - Find Arbitrage Opportunities Between NSE and BSE" />
    <meta property="og:description"
        content="Discover arbitrage opportunities between NSE and BSE by analyzing price discrepancies between stocks listed on both exchanges. Maximize profits by exploiting market inefficiencies." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Stocks vs Exchange Arbitrage - Find Arbitrage Opportunities Between NSE and BSE" />
    <meta name="twitter:description"
        content="Find arbitrage opportunities between NSE and BSE by analyzing price differences in stocks listed on both exchanges to optimize your trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="max-w-7xl mx-auto p-3">
                <!-- Page Title -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Stocks vs Exchange Arbitrage
                            </h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Find arbitrage
                                opportunities between NSE and BSE</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>

                    </div>
                </div>

                <div class="bg-white dark:bg-neutral-800 rounded-xl p-6">
                    <!-- Filter Box -->
                    <div class="mb-4">
                        <label for="filterInput" class="mr-2 font-medium">Minimum Difference %:</label>
                        <input id="filterInput" type="number" step="0.1" value="1"
                            class="border border-gray-300 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-1.5 rounded-md transition-all w-[300px]">
                    </div>

                    <!-- Main Content Area -->
                    <div>
                        <div class="overflow-x-auto">
                            <table id="debugTable"
                                class="w-full border-collapse border dark:border-neutral-600 text-sm">
                                <thead class="bg-gray-100 dark:bg-neutral-700">
                                    <tr>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50">
                                            Symbol</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            NSE Price</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            BSE Price</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Difference</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Difference %</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Recommended Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for symbol, stock in stocks.items() %}
                                    {% if symbol in bse and 'currentValue' in bse[symbol] and stock.priceInfo is defined
                                    and
                                    stock.priceInfo.lastPrice is defined %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700">
                                        <td class="border dark:border-neutral-600 p-3 text-center">{{ symbol }}</td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ stock.priceInfo.lastPrice }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ bse[symbol].currentValue }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {% set diff = stock.priceInfo.lastPrice - (bse[symbol].currentValue | float)
                                            %}
                                            {{ diff | abs | round(2) }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {% if stock.priceInfo.lastPrice != 0 %}
                                            {% set diff_percent = (diff / stock.priceInfo.lastPrice * 100) | abs |
                                            round(2)
                                            %}
                                            {% else %}
                                            {% set diff_percent = 0 %}
                                            {% endif %}
                                            {{ diff_percent }}%
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {% set nse_price = stock.priceInfo.lastPrice %}
                                            {% set bse_price = bse[symbol].currentValue | float %}
                                            {% if nse_price > bse_price %}
                                            Buy on BSE, Sell on NSE
                                            {% elif nse_price < bse_price %} Buy on NSE, Sell on BSE {% else %} No
                                                Action {% endif %} </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- FAQ Section in Accordion Format -->
                <div class="mt-12">
                    <h2 class="text-2xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is Exchange Arbitrage?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    Exchange arbitrage involves exploiting price differences of the same stock on
                                    different exchanges – in this case, between NSE and BSE.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Why are exchanges not available for day trading in this strategy?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    This strategy requires holding the stock on at least one exchange, as prices between
                                    NSE and BSE may not update instantly for day trading. It is intended for longer-term
                                    arbitrage opportunities.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How is the recommended action determined?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    The recommended action is based on the price difference between the two exchanges.
                                    If the NSE price is higher than the BSE price, the tool suggests buying on BSE and
                                    selling on NSE. If the opposite is true, it suggests buying on NSE and selling on
                                    BSE.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Where does the data come from?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    The data for NSE and BSE prices is sourced from our real-time market APIs. Minor
                                    delays may occur due to data processing.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </main>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>

        <!-- Sorting and Filtering script -->
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                var tbody = document.querySelector("#debugTable tbody");
                var rows = Array.from(tbody.querySelectorAll("tr"));

                function filterAndSort() {
                    var filterVal = parseFloat(document.querySelector("#filterInput").value);

                    // Filter rows based on the absolute difference % (column index 4)
                    rows.forEach(function (row) {
                        var diffText = row.cells[4].innerText.replace("%", "");
                        var diffValue = parseFloat(diffText);
                        if (diffValue >= filterVal) {
                            row.style.display = "";
                        } else {
                            row.style.display = "none";
                        }
                    });

                    // Get only the visible rows for sorting
                    var visibleRows = rows.filter(row => row.style.display !== "none");
                    visibleRows.sort(function (a, b) {
                        var aText = a.cells[4].innerText.replace("%", "");
                        var bText = b.cells[4].innerText.replace("%", "");
                        var aVal = parseFloat(aText);
                        var bVal = parseFloat(bText);
                        return bVal - aVal; // descending order
                    });

                    // Append the sorted visible rows back to the tbody
                    visibleRows.forEach(function (row) {
                        tbody.appendChild(row);
                    });
                }

                // Initial filter and sort
                filterAndSort();

                // Re-filter and sort whenever the filter input changes
                document.querySelector("#filterInput").addEventListener("input", function () {
                    filterAndSort();
                });
            });
        </script>


    </div>
</body>

</html>